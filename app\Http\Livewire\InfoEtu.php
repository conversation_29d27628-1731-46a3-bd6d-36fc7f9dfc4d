<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\HistoriquePayment;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithPagination;

class InfoEtu extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    public $editUser = [];
    public $query;
    public $payments;
    public $filtreNiveau;
    public $filtreAnnee;
    public $filtreRempli;

    protected $listeners = ["selectDate" => 'getSelectedDate'];

    public function updatingQuery()
    {
        $this->resetPage();
    }

    public function updatingFiltreAnnee()
    {
        $this->resetPage();
    }

    public function updatingFiltreNiveau()
    {
        $this->resetPage();
    }
    
    public function updatingFiltreRempli()
    {
        $this->resetPage();
    }


    public function render()
    {
        $persQuery = InscriptionStudent::with(['user', 'parcours', 'niveau', 'annee']);
        $filterParcours = Parcour::query();

        if ($this->query != "") {
            $persQuery->whereHas('user', function ($query) {
                $query->where('nom', 'like', '%' . $this->query . '%')
                    ->orWhere('prenom', 'like', '%' . $this->query . '%');
            });
        }

        if ($this->filtreNiveau != "") {
            $persQuery->whereNiveauId($this->filtreNiveau);
        }
        
        if ($this->filtreAnnee != "") {
            $persQuery->whereAnneeUniversitaireId($this->filtreAnnee);
        }
        
        if ($this->filtreRempli != "") {
            if ($this->filtreRempli == "non_rempli") {
                $persQuery->whereNull('parcour_id');
            } elseif ($this->filtreRempli == "rempli") {
                $persQuery->whereNotNull('parcour_id');
            }
        }

        // Apply withCount() to get etu_count
        $etuStatus = $filterParcours->withCount([
            'info as etu_count' => function ($query) {
                if ($this->filtreAnnee != "") {
                    $query->where('annee_universitaire_id', $this->filtreAnnee);
                }
                if ($this->filtreNiveau != "") {
                    $query->where('niveau_id', $this->filtreNiveau);
                }
            }
        ])->get();

        // Calculate total etu_count
        $totalEtuCount = $etuStatus->sum('etu_count');

        return view('livewire.secretaire.infoetu.index', [
            "etus" => $persQuery->whereRelation('user.roles', 'role_id', '=', 5)
                ->paginate(25),
            "etuStatus" => $etuStatus,
            "totalEtuCount" => $totalEtuCount,  // Pass total to the view

            "niveaux" => Niveau::all(),
            "annees" => AnneeUniversitaire::all(),
            "parcours" => Parcour::all(),

        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function getSelectedDate($date, $type = 'date_naissance')
    {
        if ($type === 'date_naissance') {
            $this->editUser["date_naissance"] = $date;
        } elseif ($type === 'date_delivrance') {
            $this->editUser["date_delivrance"] = $date;
        }
    }

    public function rules()
    {
        return [
            'editUser.nom' => 'required',
            'editUser.prenom' => '',
            'editUser.email' => '',
            'editUser.sexe' => 'required',
            'editUser.date_naissance' => 'required|before:01/01/14',
            'editUser.lieu_naissance' => 'required',
            'editUser.adresse' => '',
            'editUser.telephone2' => '',
            'editUser.cin' => '',
            'editUser.nom_pere' => '',
            'editUser.nom_mere' => '',
            'editUser.tel_pere' => '',
            'editUser.tel_mere' => '',
            'editUser.nom_tuteur' => '',
            'editUser.tel_tuteur' => '',
            'editUser.date_delivrance' => '',
            'editUser.lieu_delivrance' => '',
            'editUser.duplicata' => '',
            'editUser.parcour_id' => 'required',
            'editUser.niveau_id' => 'required',

            'editUser.telephone1' => ['required', Rule::unique("users", "telephone1")->ignore($this->editUser['id'] ?? null)],
        ];
    }

    public function goToListUser()
    {
        $this->currentPage = PAGELIST;
        $this->editUser = [];
    }

    public function populatePay()
    {
        $this->payments = HistoriquePayment::with(['user', 'payment'])->whereUserId($this->editUser["id"])->where(function ($query) {
            $query->where('type_payment_id', 1)
                ->orWhere('type_payment_id', 2);
        })->get();
    }

    public function goToCreateUser($id)
    {
        $user = User::find($id);
        $this->editUser = $user->toArray();
        $this->populatePay();
        $this->dispatchBrowserEvent("helperDatePicker");
        $this->currentPage = PAGECREATEFORM;
    }
    
    public function goToEditUser($id)
    {
        $this->currentPage = PAGEEDITFORM;
        $user = User::find($id);
        $this->editUser = $user->toArray();
        $this->populatePay();
        $this->dispatchBrowserEvent("helperDatePicker");
        
    }

    public function valider(HistoriquePayment $historique)
    {
        $historique->update([
            "is_valid_sec" => 1,
        ]);
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Validé avec succès!"]);
        $this->populatePay();
    }

    public function updateUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        $validationAttributes["editUser"]["photo"] = "media/avatars/avatar0.jpg";

        // Récupérer l'utilisateur existant
        $user = User::find($this->editUser["id"]);

        // Récupérer l'année universitaire actuelle de l'étudiant
        $currentInscription = InscriptionStudent::where('user_id', $this->editUser["id"])
            ->latest()
            ->first();
        $anneeUniversitaireId = $currentInscription->annee_universitaire_id ?? null;
        
        // Vérifier si l'utilisateur n'a pas déjà un matricule
        if (empty($user->matricule)) {
            // Récupérer l'année universitaire
            $anneeUniv = AnneeUniversitaire::find($anneeUniversitaireId);

            if ($anneeUniv) {
                // Compter seulement les nouveaux étudiants (ceux qui ont un seul enregistrement dans InscriptionStudent)
                // pour éviter de compter les redoublants ou les étudiants qui changent de niveau
                $count = InscriptionStudent::where('annee_universitaire_id', $anneeUniversitaireId)
                    // ->whereHas('user', function($query) {
                    //     $query->whereNull('matricule'); // Seulement ceux qui n'ont pas encore de matricule
                    // })
                    // ->groupBy('user_id')
                    ->havingRaw('COUNT(*) = 1') // Seulement ceux qui ont un seul enregistrement
                    ->count();

                    dd($anneeUniv, '', $count);

                // Incrémenter le compteur et formater avec des zéros à gauche (001, 002, etc.)
                $countFormatted = str_pad($count + 1, 3, '0', STR_PAD_LEFT);

                // Extraire uniquement la dernière partie de l'année (2024/2025 -> 25)
                $anneeFormatted = '';
                if (strpos($anneeUniv->nom, '/') !== false) {
                    $anneeParts = explode('/', $anneeUniv->nom);
                    $anneeFormatted = substr(end($anneeParts), -2);
                } else {
                    $anneeFormatted = substr($anneeUniv->nom, -2);
                }

                // Générer le matricule au nouveau format 001/25/IMSAA
                $matricule = $countFormatted . '/' . $anneeFormatted . '/IMSAA';

                // Mettre à jour le matricule
                $validationAttributes["editUser"]["matricule"] = $matricule;
            }
        }

        // Mettre à jour l'utilisateur existant
        $user->update($validationAttributes["editUser"]);
        $user->update([
            "is_filled" => 1,
            "inscription_date" => now()->format('d-m-Y'),
        ]);

        $user->info()->update([
            "niveau_id" => $this->editUser['niveau_id'],
            "parcour_id" => $this->editUser['parcour_id'],
        ]);

        $this->dispatchBrowserEvent('closeModal');
        $this->editUser = [];
        $this->currentPage = PAGELIST;
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Etudiant mis à jour avec succès!"]);
    }

    public function createUser()
    {
        // Vérifier que les informations envoyées par le formulaire sont correctes
        $validationAttributes = $this->validate();

        // Définir une photo par défaut
        $validationAttributes["editUser"]["photo"] = "media/avatars/avatar0.jpg";
        
        // Déterminer l'année universitaire actuelle
        $anneeUniversitaireId = AnneeUniversitaire::latest()->first()->id ?? null;
        
        // Générer le matricule
        $matricule = '';
        if ($anneeUniversitaireId) {
            // Récupérer l'année universitaire
            $anneeUniv = AnneeUniversitaire::find($anneeUniversitaireId);

            if ($anneeUniv) {
                // Compter seulement les nouveaux étudiants (ceux qui ont un seul enregistrement dans InscriptionStudent)
                // pour éviter de compter les redoublants ou les étudiants qui changent de niveau
                $count = InscriptionStudent::where('annee_universitaire_id', $anneeUniversitaireId)
                    ->whereHas('user', function($query) {
                        $query->whereNull('matricule'); // Seulement ceux qui n'ont pas encore de matricule
                    })
                    ->groupBy('user_id')
                    ->havingRaw('COUNT(*) = 1') // Seulement ceux qui ont un seul enregistrement
                    ->count();

                // Incrémenter le compteur et formater avec des zéros à gauche (001, 002, etc.)
                $countFormatted = str_pad($count + 1, 3, '0', STR_PAD_LEFT);

                // Extraire uniquement la dernière partie de l'année (2024/2025 -> 25)
                $anneeFormatted = '';
                if (strpos($anneeUniv->nom, '/') !== false) {
                    $anneeParts = explode('/', $anneeUniv->nom);
                    $anneeFormatted = substr(end($anneeParts), -2);
                } else {
                    $anneeFormatted = substr($anneeUniv->nom, -2);
                }

                // Générer le matricule au nouveau format 001/25/IMSAA
                $matricule = $countFormatted . '/' . $anneeFormatted . '/IMSAA';
            }
        }

        // Créer le nouvel utilisateur
        $user = User::create([
            'nom' => $this->editUser['nom'] ?? '',
            'prenom' => $this->editUser['prenom'] ?? '',
            'email' => $this->editUser['email'] ?? '',
            'sexe' => $this->editUser['sexe'] ?? '',
            'matricule' => $matricule,
            'telephone1' => $this->editUser['telephone1'] ?? '',
            'date_naissance' => $this->editUser['date_naissance'] ?? null,
            'lieu_naissance' => $this->editUser['lieu_naissance'] ?? '',
            'photo' => $validationAttributes["editUser"]["photo"],
            'is_filled' => 1,
            'inscription_date' => now()->format('d-m-Y'),
            'password' => bcrypt('password'), // Définir un mot de passe par défaut
        ]);
        
        // Associer le rôle d'étudiant (ID 5)
        $user->roles()->attach(5);
        
        // Créer l'inscription de l'étudiant
        $user->info()->create([
            'niveau_id' => $this->editUser['niveau_id'] ?? null,
            'parcour_id' => $this->editUser['parcour_id'] ?? null,
            'annee_universitaire_id' => $anneeUniversitaireId,
        ]);

        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => "Nouvel étudiant ajouté avec succès!"]);
        $this->editUser = [];
        $this->currentPage = PAGELIST;
    }
}